'use strict';

const { faker } = require('@faker-js/faker');

const dataSeedingService = ({ strapi }) => ({
  /**
   * Generate sample data for all content types
   */
  async seedAllData() {
    try {
      const results = {};

      // Seed in order to handle relationships
      console.log('🌱 Starting comprehensive data seeding...');

      // 1. First seed basic entities without relations
      results.categories = await this.seedCategories();
      results.brands = await this.seedBrands();
      results.newsCategories = await this.seedNewsCategories();

      // 2. Seed entities that depend on the above
      results.products = await this.seedProducts();
      results.articles = await this.seedArticles();
      results.promotions = await this.seedPromotions();

      // 3. Seed user-related data (requires users to exist)
      results.orders = await this.seedOrders();
      results.commissions = await this.seedCommissions();
      results.withdrawals = await this.seedWithdrawals();

      // 4. Seed audit logs
      results.auditLogs = await this.seedAuditLogs();
      results.commissionAudits = await this.seedCommissionAudits();

      // 5. Seed configuration data
      results.companySettings = await this.seedCompanySettings();
      results.taxSettings = await this.seedTaxSettings();
      results.paymentSettings = await this.seedPaymentSettings();
      results.shippingSettings = await this.seedShippingSettings();
      results.commissionSettings = await this.seedCommissionSettings();
      results.rankSettings = await this.seedRankSettings();
      results.zaloSettings = await this.seedZaloSettings();
      results.uiSettings = await this.seedUISettings();

      console.log('✅ Data seeding completed successfully!');

      return {
        success: true,
        message: 'All sample data has been generated successfully',
        data: results,
        summary: this.generateSummary(results),
      };
    } catch (error) {
      console.error('❌ Error during data seeding:', error);
      throw new Error(`Data seeding failed: ${error.message}`);
    }
  },

  /**
   * Seed product categories (Danh mục sản phẩm)
   */
  async seedCategories() {
    try {
      // Check if categories already exist
      const existing = await strapi.entityService.findMany(
        'api::danh-muc-san-pham.danh-muc-san-pham',
        { limit: 1 }
      );
      if (existing && existing.length > 0) {
        console.log('📦 Categories already exist, skipping...');
        return { skipped: true, count: existing.length };
      }

      const categories = [
        'Điện thoại & Smartphone',
        'Laptop & Máy tính',
        'Tablet & iPad',
        'Phụ kiện công nghệ',
        'Đồng hồ thông minh',
        'Tai nghe & Audio',
        'Camera & Quay phim',
        'Gaming & Esports',
        'Thiết bị mạng',
        'Linh kiện máy tính',
      ];

      const createdCategories = [];
      for (const categoryName of categories) {
        const category = await strapi.entityService.create(
          'api::danh-muc-san-pham.danh-muc-san-pham',
          {
            data: {
              name: categoryName,
              isActive: faker.datatype.boolean(0.9), // 90% chance of being active
            },
          }
        );
        createdCategories.push(category);
      }

      console.log(`✅ Created ${createdCategories.length} product categories`);
      return {
        created: true,
        count: createdCategories.length,
        data: createdCategories,
      };
    } catch (error) {
      console.error('Error seeding categories:', error);
      throw error;
    }
  },

  /**
   * Seed brands (Thương hiệu)
   */
  async seedBrands() {
    try {
      const existing = await strapi.entityService.findMany(
        'api::thuong-hieu.thuong-hieu',
        { limit: 1 }
      );
      if (existing && existing.length > 0) {
        console.log('🏷️ Brands already exist, skipping...');
        return { skipped: true, count: existing.length };
      }

      const brands = [
        {
          name: 'Apple',
          website: 'https://apple.com',
          description: 'Thiết kế đẳng cấp, công nghệ tiên tiến',
        },
        {
          name: 'Samsung',
          website: 'https://samsung.com',
          description: 'Đổi mới không ngừng, chất lượng hàng đầu',
        },
        {
          name: 'Xiaomi',
          website: 'https://mi.com',
          description: 'Công nghệ cho mọi người',
        },
        {
          name: 'OPPO',
          website: 'https://oppo.com',
          description: 'Camera selfie chuyên nghiệp',
        },
        {
          name: 'Vivo',
          website: 'https://vivo.com',
          description: 'Âm nhạc và nhiếp ảnh hoàn hảo',
        },
        {
          name: 'Huawei',
          website: 'https://huawei.com',
          description: 'Kết nối thế giới thông minh',
        },
        {
          name: 'Realme',
          website: 'https://realme.com',
          description: 'Dare to leap',
        },
        {
          name: 'OnePlus',
          website: 'https://oneplus.com',
          description: 'Never Settle',
        },
        {
          name: 'Sony',
          website: 'https://sony.com',
          description: 'Giải trí và công nghệ',
        },
        { name: 'LG', website: 'https://lg.com', description: "Life's Good" },
      ];

      const createdBrands = [];
      for (const brandData of brands) {
        const brand = await strapi.entityService.create(
          'api::thuong-hieu.thuong-hieu',
          {
            data: {
              ...brandData,
              isActive: faker.datatype.boolean(0.85), // 85% chance of being active
            },
          }
        );
        createdBrands.push(brand);
      }

      console.log(`✅ Created ${createdBrands.length} brands`);
      return {
        created: true,
        count: createdBrands.length,
        data: createdBrands,
      };
    } catch (error) {
      console.error('Error seeding brands:', error);
      throw error;
    }
  },

  /**
   * Seed news categories (Danh mục tin tức)
   */
  async seedNewsCategories() {
    try {
      const existing = await strapi.entityService.findMany(
        'api::danh-muc-tin-tuc.danh-muc-tin-tuc',
        { limit: 1 }
      );
      if (existing && existing.length > 0) {
        console.log('📰 News categories already exist, skipping...');
        return { skipped: true, count: existing.length };
      }

      const newsCategories = [
        {
          name: 'Tin tức công nghệ',
          description: 'Cập nhật tin tức mới nhất về công nghệ',
        },
        {
          name: 'Đánh giá sản phẩm',
          description: 'Đánh giá chi tiết các sản phẩm công nghệ',
        },
        {
          name: 'Hướng dẫn sử dụng',
          description: 'Hướng dẫn sử dụng các thiết bị công nghệ',
        },
        {
          name: 'Khuyến mãi hot',
          description: 'Thông tin về các chương trình khuyến mãi',
        },
        { name: 'Xu hướng 2024', description: 'Xu hướng công nghệ năm 2024' },
        {
          name: 'So sánh sản phẩm',
          description: 'So sánh các sản phẩm cùng phân khúc',
        },
        {
          name: 'Tips & Tricks',
          description: 'Mẹo và thủ thuật sử dụng công nghệ',
        },
        { name: 'Tin tức Apple', description: 'Tin tức về các sản phẩm Apple' },
        {
          name: 'Tin tức Android',
          description: 'Tin tức về hệ sinh thái Android',
        },
        {
          name: 'Gaming News',
          description: 'Tin tức về game và thiết bị gaming',
        },
      ];

      const createdNewsCategories = [];
      for (const categoryData of newsCategories) {
        const category = await strapi.entityService.create(
          'api::danh-muc-tin-tuc.danh-muc-tin-tuc',
          {
            data: {
              ...categoryData,
              isActive: faker.datatype.boolean(0.9),
            },
          }
        );
        createdNewsCategories.push(category);
      }

      console.log(`✅ Created ${createdNewsCategories.length} news categories`);
      return {
        created: true,
        count: createdNewsCategories.length,
        data: createdNewsCategories,
      };
    } catch (error) {
      console.error('Error seeding news categories:', error);
      throw error;
    }
  },

  /**
   * Seed products (Sản phẩm)
   */
  async seedProducts() {
    try {
      const existing = await strapi.entityService.findMany(
        'api::san-pham.san-pham',
        { limit: 1 }
      );
      if (existing && existing.length > 0) {
        console.log('📱 Products already exist, skipping...');
        return { skipped: true, count: existing.length };
      }

      // Get categories and brands for relationships
      const categories = await strapi.entityService.findMany(
        'api::danh-muc-san-pham.danh-muc-san-pham'
      );
      const brands = await strapi.entityService.findMany(
        'api::thuong-hieu.thuong-hieu'
      );

      if (!categories.length || !brands.length) {
        throw new Error('Categories and brands must be seeded first');
      }

      const productTemplates = [
        {
          name: 'iPhone 15 Pro Max',
          basePrice: 29990000,
          category: 'Điện thoại',
          brand: 'Apple',
        },
        {
          name: 'Samsung Galaxy S24 Ultra',
          basePrice: 26990000,
          category: 'Điện thoại',
          brand: 'Samsung',
        },
        {
          name: 'MacBook Pro M3',
          basePrice: 45990000,
          category: 'Laptop',
          brand: 'Apple',
        },
        {
          name: 'Dell XPS 13',
          basePrice: 25990000,
          category: 'Laptop',
          brand: 'Dell',
        },
        {
          name: 'iPad Pro 12.9',
          basePrice: 24990000,
          category: 'Tablet',
          brand: 'Apple',
        },
        {
          name: 'AirPods Pro 2',
          basePrice: 5990000,
          category: 'Phụ kiện',
          brand: 'Apple',
        },
        {
          name: 'Sony WH-1000XM5',
          basePrice: 7990000,
          category: 'Tai nghe',
          brand: 'Sony',
        },
        {
          name: 'Apple Watch Series 9',
          basePrice: 9990000,
          category: 'Đồng hồ',
          brand: 'Apple',
        },
        {
          name: 'Xiaomi 14 Ultra',
          basePrice: 18990000,
          category: 'Điện thoại',
          brand: 'Xiaomi',
        },
        {
          name: 'ASUS ROG Strix',
          basePrice: 35990000,
          category: 'Gaming',
          brand: 'ASUS',
        },
      ];

      const createdProducts = [];
      for (const template of productTemplates) {
        // Find matching category and brand
        const category = categories.find((c) =>
          c.name.includes(template.category.split(' ')[0])
        );
        const brand =
          brands.find((b) => b.name === template.brand) || brands[0];

        const discountPercent = faker.number.int({ min: 5, max: 30 });
        const salePrice = template.basePrice * (1 - discountPercent / 100);

        const product = await strapi.entityService.create(
          'api::san-pham.san-pham',
          {
            data: {
              name: template.name,
              gia_goc: template.basePrice,
              gia_ban: Math.round(salePrice),
              so_luong_ton_kho: faker.number.int({ min: 10, max: 100 }),
              da_ban: faker.number.int({ min: 0, max: 50 }),
              danh_muc: category?.id,
              thuong_hieu: brand?.id,
              hot: faker.datatype.boolean(0.3), // 30% chance of being hot
              isActive: faker.datatype.boolean(0.9),
              mo_ta: faker.lorem.paragraphs(2),
              luot_xem: faker.number.int({ min: 100, max: 5000 }),
              luot_yeu_thich: faker.number.int({ min: 10, max: 500 }),
            },
          }
        );
        createdProducts.push(product);
      }

      console.log(`✅ Created ${createdProducts.length} products`);
      return {
        created: true,
        count: createdProducts.length,
        data: createdProducts,
      };
    } catch (error) {
      console.error('Error seeding products:', error);
      throw error;
    }
  },

  /**
   * Seed articles (Bài viết)
   */
  async seedArticles() {
    try {
      const existing = await strapi.entityService.findMany(
        'api::bai-viet.bai-viet',
        { limit: 1 }
      );
      if (existing && existing.length > 0) {
        console.log('📝 Articles already exist, skipping...');
        return { skipped: true, count: existing.length };
      }

      const newsCategories = await strapi.entityService.findMany(
        'api::danh-muc-tin-tuc.danh-muc-tin-tuc'
      );
      if (!newsCategories.length) {
        throw new Error('News categories must be seeded first');
      }

      const articleTitles = [
        'iPhone 15 Pro Max: Đánh giá chi tiết sau 1 tháng sử dụng',
        'So sánh Samsung Galaxy S24 Ultra vs iPhone 15 Pro Max',
        'Top 10 laptop gaming tốt nhất năm 2024',
        'Hướng dẫn chọn mua tai nghe không dây phù hợp',
        'Apple Watch Series 9: Có đáng để nâng cấp?',
        'Xu hướng công nghệ AI trong smartphone 2024',
        'Cách tối ưu hóa pin cho điện thoại Android',
        'Đánh giá MacBook Pro M3: Hiệu năng vượt trội',
        'Tips chụp ảnh đẹp với camera smartphone',
        'Tương lai của công nghệ 5G tại Việt Nam',
      ];

      const createdArticles = [];
      for (const title of articleTitles) {
        const randomCategory =
          newsCategories[
            faker.number.int({ min: 0, max: newsCategories.length - 1 })
          ];

        const article = await strapi.entityService.create(
          'api::bai-viet.bai-viet',
          {
            data: {
              title,
              content: faker.lorem.paragraphs(5),
              hot: faker.datatype.boolean(0.2), // 20% chance of being hot
              danh_muc: randomCategory.id,
              isActive: faker.datatype.boolean(0.95),
              publishedAt: faker.datatype.boolean(0.8) ? new Date() : null, // 80% published
            },
          }
        );
        createdArticles.push(article);
      }

      console.log(`✅ Created ${createdArticles.length} articles`);
      return {
        created: true,
        count: createdArticles.length,
        data: createdArticles,
      };
    } catch (error) {
      console.error('Error seeding articles:', error);
      throw error;
    }
  },

  /**
   * Seed promotions (Khuyến mãi)
   */
  async seedPromotions() {
    try {
      const existing = await strapi.entityService.findMany(
        'api::khuyen-mai.khuyen-mai',
        { limit: 1 }
      );
      if (existing && existing.length > 0) {
        console.log('🎁 Promotions already exist, skipping...');
        return { skipped: true, count: existing.length };
      }

      const promotionData = [
        {
          name: 'Giảm 10% đơn hàng đầu tiên',
          code: 'FIRST10',
          type: 'percentage',
          value: 10,
          minOrder: 100000,
        },
        {
          name: 'Giảm 50K cho đơn từ 500K',
          code: 'SAVE50K',
          type: 'fixed_amount',
          value: 50000,
          minOrder: 500000,
        },
        {
          name: 'Miễn phí vận chuyển',
          code: 'FREESHIP',
          type: 'free_shipping',
          value: 0,
          minOrder: 200000,
        },
        {
          name: 'Giảm 15% cuối tuần',
          code: 'WEEKEND15',
          type: 'percentage',
          value: 15,
          minOrder: 300000,
        },
        {
          name: 'Flash Sale 20%',
          code: 'FLASH20',
          type: 'percentage',
          value: 20,
          minOrder: 150000,
        },
        {
          name: 'Giảm 100K đơn từ 1 triệu',
          code: 'VIP100K',
          type: 'fixed_amount',
          value: 100000,
          minOrder: 1000000,
        },
        {
          name: 'Khuyến mãi sinh nhật',
          code: 'BIRTHDAY',
          type: 'percentage',
          value: 25,
          minOrder: 200000,
        },
        {
          name: 'Ưu đãi thành viên mới',
          code: 'NEWMEMBER',
          type: 'percentage',
          value: 12,
          minOrder: 100000,
        },
        {
          name: 'Giảm giá Black Friday',
          code: 'BLACKFRIDAY',
          type: 'percentage',
          value: 30,
          minOrder: 500000,
        },
        {
          name: 'Combo deal đặc biệt',
          code: 'COMBO2024',
          type: 'fixed_amount',
          value: 200000,
          minOrder: 1500000,
        },
      ];

      const createdPromotions = [];
      for (const promoData of promotionData) {
        const startDate = faker.date.recent({ days: 30 });
        const endDate = faker.date.future({ years: 0.5, refDate: startDate });

        const promotion = await strapi.entityService.create(
          'api::khuyen-mai.khuyen-mai',
          {
            data: {
              name: promoData.name,
              description: `Khuyến mãi ${promoData.name.toLowerCase()} - Áp dụng cho tất cả sản phẩm`,
              code: promoData.code,
              type: promoData.type,
              value: promoData.value,
              minOrderAmount: promoData.minOrder,
              maxDiscountAmount:
                promoData.type === 'percentage'
                  ? promoData.minOrder * 0.5
                  : null,
              usageLimit: faker.number.int({ min: 50, max: 1000 }),
              usageCount: faker.number.int({ min: 0, max: 20 }),
              startDate,
              endDate,
              isActive: faker.datatype.boolean(0.8),
              isPublic: faker.datatype.boolean(0.9),
            },
          }
        );
        createdPromotions.push(promotion);
      }

      console.log(`✅ Created ${createdPromotions.length} promotions`);
      return {
        created: true,
        count: createdPromotions.length,
        data: createdPromotions,
      };
    } catch (error) {
      console.error('Error seeding promotions:', error);
      throw error;
    }
  },

  /**
   * Seed orders (Đơn hàng)
   */
  async seedOrders() {
    try {
      const existing = await strapi.entityService.findMany(
        'api::don-hang.don-hang',
        { limit: 1 }
      );
      if (existing && existing.length > 0) {
        console.log('🛒 Orders already exist, skipping...');
        return { skipped: true, count: existing.length };
      }

      // Get users for relationships
      const users = await strapi.entityService.findMany(
        'plugin::users-permissions.user',
        { limit: 20 }
      );
      if (!users.length) {
        console.log(
          '⚠️ No users found, creating sample orders without user relationships'
        );
      }

      const orderStatuses = [
        'Chờ xác nhận',
        'Chờ giao hàng',
        'Đang giao hàng',
        'Đã hoàn thành',
        'Đã hủy',
      ];

      const createdOrders = [];
      for (let i = 1; i <= 10; i++) {
        const baseAmount = faker.number.int({ min: 100000, max: 5000000 });
        const taxRate = 0.1; // 10% tax
        const taxAmount = baseAmount * taxRate;
        const discountAmount = faker.number.int({
          min: 0,
          max: baseAmount * 0.2,
        });
        const shippingAmount = faker.number.int({ min: 0, max: 50000 });
        const priceAfterTax =
          baseAmount + taxAmount - discountAmount + shippingAmount;

        const order = await strapi.entityService.create(
          'api::don-hang.don-hang',
          {
            data: {
              code: `DH${Date.now()}${i.toString().padStart(3, '0')}`,
              priceAfterTax: Math.round(priceAfterTax),
              taxAmount: Math.round(taxAmount),
              discountAmount: Math.round(discountAmount),
              shippingAmount: Math.round(shippingAmount),
              productSnapshot: {
                items: [
                  {
                    id: faker.number.int({ min: 1, max: 10 }),
                    name: faker.commerce.productName(),
                    price: faker.number.int({ min: 50000, max: 1000000 }),
                    quantity: faker.number.int({ min: 1, max: 5 }),
                  },
                ],
              },
              customerSnapshot: {
                name: faker.person.fullName(),
                phone: faker.phone.number(),
                address: faker.location.streetAddress(),
                email: faker.internet.email(),
              },
              statusOrder:
                orderStatuses[
                  faker.number.int({ min: 0, max: orderStatuses.length - 1 })
                ],
              paymentStatus: faker.datatype.boolean(0.7), // 70% paid
              user:
                users.length > 0
                  ? users[faker.number.int({ min: 0, max: users.length - 1 })]
                      ?.id
                  : null,
            },
          }
        );
        createdOrders.push(order);
      }

      console.log(`✅ Created ${createdOrders.length} orders`);
      return {
        created: true,
        count: createdOrders.length,
        data: createdOrders,
      };
    } catch (error) {
      console.error('Error seeding orders:', error);
      throw error;
    }
  },

  /**
   * Seed commissions (Hoa hồng)
   */
  async seedCommissions() {
    try {
      const existing = await strapi.entityService.findMany(
        'api::hoa-hong.hoa-hong',
        { limit: 1 }
      );
      if (existing && existing.length > 0) {
        console.log('💰 Commissions already exist, skipping...');
        return { skipped: true, count: existing.length };
      }

      const orders = await strapi.entityService.findMany(
        'api::don-hang.don-hang'
      );
      const users = await strapi.entityService.findMany(
        'plugin::users-permissions.user',
        { limit: 20 }
      );

      if (!orders.length || !users.length) {
        console.log(
          '⚠️ Orders or users not found, skipping commission seeding'
        );
        return { skipped: true, count: 0 };
      }

      const commissionTypes = ['referral', 'company'];
      const statusOptions = ['pending', 'paid', 'cancelled'];

      const createdCommissions = [];
      for (let i = 0; i < 10; i++) {
        const randomOrder =
          orders[faker.number.int({ min: 0, max: orders.length - 1 })];
        const randomUser =
          users[faker.number.int({ min: 0, max: users.length - 1 })];
        const percentage = faker.number.float({
          min: 1,
          max: 15,
          precision: 0.1,
        });
        const amount = Math.round(
          randomOrder.priceAfterTax * (percentage / 100)
        );

        const commission = await strapi.entityService.create(
          'api::hoa-hong.hoa-hong',
          {
            data: {
              order: randomOrder.id,
              user: randomUser.id,
              amount,
              percentage,
              statusPaid:
                statusOptions[
                  faker.number.int({ min: 0, max: statusOptions.length - 1 })
                ],
              type: commissionTypes[
                faker.number.int({ min: 0, max: commissionTypes.length - 1 })
              ],
            },
          }
        );
        createdCommissions.push(commission);
      }

      console.log(`✅ Created ${createdCommissions.length} commissions`);
      return {
        created: true,
        count: createdCommissions.length,
        data: createdCommissions,
      };
    } catch (error) {
      console.error('Error seeding commissions:', error);
      throw error;
    }
  },

  /**
   * Seed withdrawals (Rút tiền)
   */
  async seedWithdrawals() {
    try {
      const existing = await strapi.entityService.findMany(
        'api::rut-tien.rut-tien',
        { limit: 1 }
      );
      if (existing && existing.length > 0) {
        console.log('🏦 Withdrawals already exist, skipping...');
        return { skipped: true, count: existing.length };
      }

      const users = await strapi.entityService.findMany(
        'plugin::users-permissions.user',
        { limit: 20 }
      );
      const adminUsers = await strapi.entityService.findMany('admin::user', {
        limit: 5,
      });

      if (!users.length) {
        console.log('⚠️ No users found, skipping withdrawal seeding');
        return { skipped: true, count: 0 };
      }

      const statusOptions = ['pending', 'approved', 'rejected'];
      const banks = [
        'Vietcombank',
        'Techcombank',
        'BIDV',
        'VietinBank',
        'Agribank',
        'MB Bank',
        'ACB',
        'TPBank',
      ];

      const createdWithdrawals = [];
      for (let i = 0; i < 10; i++) {
        const randomUser =
          users[faker.number.int({ min: 0, max: users.length - 1 })];
        const randomBank =
          banks[faker.number.int({ min: 0, max: banks.length - 1 })];
        const status =
          statusOptions[
            faker.number.int({ min: 0, max: statusOptions.length - 1 })
          ];

        const withdrawal = await strapi.entityService.create(
          'api::rut-tien.rut-tien',
          {
            data: {
              user: randomUser.id,
              amount: faker.number.int({ min: 100000, max: ******** }),
              bankName: randomBank,
              bankAccount: faker.finance.accountNumber(10),
              accountHolder: faker.person.fullName(),
              statusPaid: status,
              note: faker.lorem.sentence(),
              adminNote: status !== 'pending' ? faker.lorem.sentence() : null,
              processedAt: status !== 'pending' ? faker.date.recent() : null,
              processedBy:
                status !== 'pending' && adminUsers.length > 0
                  ? adminUsers[
                      faker.number.int({ min: 0, max: adminUsers.length - 1 })
                    ]?.id
                  : null,
            },
          }
        );
        createdWithdrawals.push(withdrawal);
      }

      console.log(`✅ Created ${createdWithdrawals.length} withdrawals`);
      return {
        created: true,
        count: createdWithdrawals.length,
        data: createdWithdrawals,
      };
    } catch (error) {
      console.error('Error seeding withdrawals:', error);
      throw error;
    }
  },

  /**
   * Generate summary of seeded data
   */
  generateSummary(results) {
    const summary = {};
    let totalCreated = 0;

    Object.keys(results).forEach((key) => {
      const result = results[key];
      if (result.created) {
        summary[key] = `Created ${result.count} records`;
        totalCreated += result.count;
      } else if (result.skipped) {
        summary[key] = `Skipped (${result.count} existing records)`;
      } else {
        summary[key] = 'Unknown status';
      }
    });

    summary.total = `Total ${totalCreated} new records created`;
    return summary;
  },
});

module.exports = dataSeedingService;
